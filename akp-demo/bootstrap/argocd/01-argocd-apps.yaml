apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: guestbook-dev
  namespace: argocd
  annotations:
    kargo.akuity.io/authorized-stage: akp-demo:dev
spec:
  project: akp-demo
  source:
    repoURL: https://github.com/akuity/akp-demo.git
    targetRevision: HEAD
    path: charts/guestbook
    helm:
      releaseName: dev
      valueFiles:
      - values-dev.yaml
  destination:
    name: demo
    namespace: akp-demo
  syncPolicy:
    syncOptions:
    - CreateNamespace=true

---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: guestbook-staging
  namespace: argocd
  annotations:
    kargo.akuity.io/authorized-stage: akp-demo:staging
spec:
  project: akp-demo
  source:
    repoURL: https://github.com/akuity/akp-demo.git
    targetRevision: HEAD
    path: charts/guestbook
    helm:
      releaseName: staging
      valueFiles:
      - values-staging.yaml
  destination:
    name: demo
    namespace: akp-demo
  syncPolicy:
    syncOptions:
    - CreateNamespace=true

---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: guestbook-prod
  namespace: argocd
  annotations:
    kargo.akuity.io/authorized-stage: akp-demo:prod
spec:
  project: akp-demo
  source:
    repoURL: https://github.com/akuity/akp-demo.git
    targetRevision: HEAD
    path: charts/guestbook
    helm:
      releaseName: prod
      valueFiles:
      - values-prod.yaml
  destination:
    name: demo
    namespace: akp-demo
  syncPolicy:
    syncOptions:
    - CreateNamespace=true

---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: guestbook-oom
  namespace: argocd
  annotations:
    kargo.akuity.io/authorized-stage: akp-demo:oom
spec:
  project: akp-demo
  source:
    repoURL: https://github.com/akuity/akp-demo.git
    targetRevision: HEAD
    path: oom-demo/
  destination:
    name: demo
    namespace: akp-demo
  syncPolicy:
    syncOptions:
    - CreateNamespace=true

---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: guestbook-crashloop
  namespace: argocd
  annotations:
    kargo.akuity.io/authorized-stage: akp-demo:crashloop
spec:
  project: akp-demo
  source:
    repoURL: https://github.com/akuity/akp-demo.git
    targetRevision: HEAD
    path: crashloop-demo/
  destination:
    name: demo
    namespace: akp-demo
  syncPolicy:
    syncOptions:
    - CreateNamespace=true
