---
apiVersion: kargo.akuity.io/v1alpha1
kind: Stage
metadata:
  name: dev
  namespace: akp-demo
spec:
  requestedFreight:
  - origin:
      kind: Warehouse
      name: guestbook
    sources:
      direct: true
  promotionTemplate:
    spec:
      steps:
      - task:
          name: promote

---
apiVersion: kargo.akuity.io/v1alpha1
kind: Stage
metadata:
  name: staging
  namespace: akp-demo
spec:
  requestedFreight:
  - origin:
      kind: Warehouse
      name: guestbook
    sources:
      stages:
      - dev
  promotionTemplate:
    spec:
      steps:
      - task:
          name: promote

---
apiVersion: kargo.akuity.io/v1alpha1
kind: Stage
metadata:
  name: prod
  namespace: akp-demo
spec:
  requestedFreight:
  - origin:
      kind: Warehouse
      name: guestbook
    sources:
      stages:
      - staging
  promotionTemplate:
    spec:
      steps:
      - task:
          name: promote

---
apiVersion: kargo.akuity.io/v1alpha1
kind: Stage
metadata:
  name: oom
  namespace: akp-demo
spec:
  requestedFreight:
  - origin:
      kind: Warehouse
      name: guestbook
    sources:
      direct: true
  promotionTemplate:
    spec:
      steps:
      - task:
          name: promote-sync-only

---
apiVersion: kargo.akuity.io/v1alpha1
kind: Stage
metadata:
  name: crashloop
  namespace: akp-demo
spec:
  requestedFreight:
  - origin:
      kind: Warehouse
      name: guestbook
    sources:
      direct: true
  promotionTemplate:
    spec:
      steps:
      - task:
          name: promote-sync-only
