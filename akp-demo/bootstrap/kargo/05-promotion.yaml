apiVersion: kargo.akuity.io/v1alpha1
kind: Promotion
metadata:
  annotations:
    kargo.akuity.io/create-actor: admin
    argocd.argoproj.io/sync-wave: "2"
  name: dev.01jz8zrm16tq35vfp9dngxnjz4.4e67f4d
  namespace: akp-demo
spec:
  freight: 4e67f4de17e873b4481de67e7f85244be6ce3009
  stage: dev
  steps:
  - as: task-1::step-1
    config:
      apps:
      - name: guestbook-${{ ctx.stage }}
        sources:
        - helm:
            images:
            - key: image.tag
              value: ${{ imageFrom("ghcr.io/akuity/guestbook").Tag }}
          repoURL: https://github.com/akuity/akp-demo.git
    uses: argocd-update
