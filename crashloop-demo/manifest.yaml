apiVersion: apps/v1
kind: Deployment
metadata:
  name: crashloop-demo
spec:
  minReadySeconds: 10
  progressDeadlineSeconds: 20
  selector:
    matchLabels:
      app.kubernetes.io/name: crashloop-demo
  template:
    metadata:
      labels:
        app.kubernetes.io/name: crashloop-demo
    spec:
      containers:
        - name: guestbook
          image: ghcr.io/akuity/guestbook:v0.0.2
          command:
          - /app/guestbook
          args:
          - --port=3000
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 2
            httpGet:
              path: /
              port: 30000
              scheme: HTTP
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 1
          readinessProbe:
            httpGet:
              path: /
              port: 30000
          resources:
            limits:
              cpu: 100m
              memory: 32Mi
            requests:
              cpu: 10m
              memory: 32Mi
          securityContext:
            capabilities:
              drop:
                - ALL
            readOnlyRootFilesystem: true
            runAsNonRoot: true
            runAsUser: 1000
