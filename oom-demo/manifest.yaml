apiVersion: apps/v1
kind: Deployment
metadata:
  name: oom-demo
spec:
  minReadySeconds: 10
  progressDeadlineSeconds: 20
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/instance: oom
      app.kubernetes.io/name: oom-demo
  template:
    metadata:
      labels:
        app.kubernetes.io/instance: oom
        app.kubernetes.io/name: oom-demo
    spec:
      containers:
        - args:
            - '--vm'
            - '1'
            - '--vm-bytes'
            - 150M
            - '--vm-hang'
            - '1'
          command:
            - stress
          image: polinux/stress
          imagePullPolicy: Always
          name: oom
          resources:
            limits:
              cpu: 100m
              memory: 128Mi
            requests:
              cpu: 100m
              memory: 128Mi
          securityContext:
            capabilities:
              drop:
                - ALL
            readOnlyRootFilesystem: true
            runAsNonRoot: true
            runAsUser: 1000
